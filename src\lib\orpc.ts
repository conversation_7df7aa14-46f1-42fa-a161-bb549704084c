import type { Session } from "@repo/auth";
import { oz } from "@orpc/zod";

// Define your context type to match the auth system
export type Context = {
	userId?: string;
	user?: Session["user"];
	session?: Session["session"];
	req?: Request;
	// Add other context properties
};

// Base ORPC procedure with context
export const publicProcedure = oz.context<Context>();

// Protected procedure with auth check
export const protectedProcedure = publicProcedure.use(
	async (_input, context, meta) => {
		if (!context.userId) {
			throw new Error("Unauthorized");
		}
		return meta.next({ context });
	},
);
