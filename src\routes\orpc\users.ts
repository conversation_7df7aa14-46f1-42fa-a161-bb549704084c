import { countAllUsers, getUsers } from "@repo/database";
import { z } from "zod";
import { protectedProcedure, publicProcedure } from "../../lib/orpc";

export const usersRouter = {
	// Public procedure to get current user info
	me: protectedProcedure.output(
		z.object({
			id: z.string(),
			email: z.string(),
			name: z.string().nullable(),
			role: z.string(),
		}),
	).handler(async ({ context }) => {
		if (!context.user) {
			throw new Error("User not found in context");
		}

		return {
			id: context.user.id,
			email: context.user.email,
			name: context.user.name,
			role: context.user.role,
		};
	}),

	// Admin-only procedure to list all users
	list: protectedProcedure
		.input(
			z.object({
				query: z.string().optional(),
				limit: z.number().min(1).max(100).default(10),
				offset: z.number().min(0).default(0),
			}),
		)
		.output(
			z.object({
				users: z.array(
					z.object({
						id: z.string(),
						email: z.string(),
						name: z.string().nullable(),
						role: z.string(),
						createdAt: z.date(),
						updatedAt: z.date(),
					}),
				),
				total: z.number(),
			}),
		)
		.handler(async ({ input, context }) => {
			// Check if user is admin
			if (context.user?.role !== "admin") {
				throw new Error("Forbidden: Admin access required");
			}

			const { query, limit, offset } = input;

			const users = await getUsers({
				query,
				limit,
				offset,
			});

			const total = await countAllUsers();

			return {
				users,
				total,
			};
		}),

	// Public procedure to check if service is healthy
	health: publicProcedure
		.output(z.object({ status: z.string(), timestamp: z.date() }))
		.handler(async () => {
			return {
				status: "ok",
				timestamp: new Date(),
			};
		}),
};
