import {
	AiChatSchema,
	createAiChat,
	deleteAiChat,
	getAiChatById,
	getAiChatsByUserId,
	updateAiChat,
} from "@repo/database";
import { z } from "zod";
import { protectedProcedure } from "../../lib/orpc";

const MessageSchema = z.object({
	role: z.enum(["user", "assistant"]),
	content: z.string(),
});

const ChatSchema = AiChatSchema.extend({
	messages: z.array(MessageSchema),
});

export const chatsRouter = {
	// Get all chats for the current user
	list: protectedProcedure
		.input(
			z.object({
				limit: z.number().min(1).max(100).default(10),
				offset: z.number().min(0).default(0),
			}),
		)
		.output(z.object({ chats: z.array(ChatSchema) }))
		.handler(async ({ input, context }) => {
			if (!context.userId) {
				throw new Error("User not authenticated");
			}

			const { limit, offset } = input;

			const chats = await getAiChatsByUserId({
				userId: context.userId,
				limit,
				offset,
			});

			return { chats };
		}),

	// Get a specific chat by ID
	get: protectedProcedure
		.input(z.object({ id: z.string() }))
		.output(z.object({ chat: ChatSchema }))
		.handler(async ({ input, context }) => {
			if (!context.userId) {
				throw new Error("User not authenticated");
			}

			const { id } = input;
			const chat = await getAiChatById(id);

			if (!chat) {
				throw new Error("Chat not found");
			}

			// Check if user owns the chat
			if (chat.userId !== context.userId) {
				throw new Error("Forbidden: You don't have access to this chat");
			}

			return { chat };
		}),

	// Create a new chat
	create: protectedProcedure
		.input(
			z.object({
				title: z.string().optional(),
				organizationId: z.string().optional(),
			}),
		)
		.output(z.object({ chat: ChatSchema }))
		.handler(async ({ input, context }) => {
			if (!context.userId) {
				throw new Error("User not authenticated");
			}

			const { title, organizationId } = input;

			const chat = await createAiChat({
				title,
				organizationId,
				userId: context.userId,
			});

			if (!chat) {
				throw new Error("Failed to create chat");
			}

			return { chat };
		}),

	// Update a chat
	update: protectedProcedure
		.input(
			z.object({
				id: z.string(),
				title: z.string().optional(),
				messages: z.array(MessageSchema).optional(),
			}),
		)
		.output(z.object({ chat: ChatSchema }))
		.handler(async ({ input, context }) => {
			if (!context.userId) {
				throw new Error("User not authenticated");
			}

			const { id, title, messages } = input;

			// First check if chat exists and user owns it
			const existingChat = await getAiChatById(id);
			if (!existingChat) {
				throw new Error("Chat not found");
			}

			if (existingChat.userId !== context.userId) {
				throw new Error("Forbidden: You don't have access to this chat");
			}

			const chat = await updateAiChat({
				id,
				title,
				messages,
			});

			if (!chat) {
				throw new Error("Failed to update chat");
			}

			return { chat };
		}),

	// Delete a chat
	delete: protectedProcedure
		.input(z.object({ id: z.string() }))
		.output(z.object({ success: z.boolean() }))
		.handler(async ({ input, context }) => {
			if (!context.userId) {
				throw new Error("User not authenticated");
			}

			const { id } = input;

			// First check if chat exists and user owns it
			const existingChat = await getAiChatById(id);
			if (!existingChat) {
				throw new Error("Chat not found");
			}

			if (existingChat.userId !== context.userId) {
				throw new Error("Forbidden: You don't have access to this chat");
			}

			await deleteAiChat(id);

			return { success: true };
		}),
};
