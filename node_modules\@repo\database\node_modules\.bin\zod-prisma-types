#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/zod-prisma-types@3.2.4_@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3___43t5wic2lswf27s43nz7qpgc5i/node_modules/zod-prisma-types/dist/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/zod-prisma-types@3.2.4_@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3___43t5wic2lswf27s43nz7qpgc5i/node_modules/zod-prisma-types/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/zod-prisma-types@3.2.4_@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3___43t5wic2lswf27s43nz7qpgc5i/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/zod-prisma-types@3.2.4_@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3___43t5wic2lswf27s43nz7qpgc5i/node_modules/zod-prisma-types/dist/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/zod-prisma-types@3.2.4_@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3___43t5wic2lswf27s43nz7qpgc5i/node_modules/zod-prisma-types/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/zod-prisma-types@3.2.4_@prisma+client@6.9.0_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3___43t5wic2lswf27s43nz7qpgc5i/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../zod-prisma-types/dist/bin.js" "$@"
else
  exec node  "$basedir/../zod-prisma-types/dist/bin.js" "$@"
fi
