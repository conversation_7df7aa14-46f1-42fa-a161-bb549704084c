@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\next@15.3.3_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19.2.0_react@19.2.0__react@19.2.0\node_modules\next\dist\bin\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\next@15.3.3_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19.2.0_react@19.2.0__react@19.2.0\node_modules\next\dist\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\next@15.3.3_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19.2.0_react@19.2.0__react@19.2.0\node_modules\next\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\next@15.3.3_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19.2.0_react@19.2.0__react@19.2.0\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\next@15.3.3_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19.2.0_react@19.2.0__react@19.2.0\node_modules\next\dist\bin\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\next@15.3.3_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19.2.0_react@19.2.0__react@19.2.0\node_modules\next\dist\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\next@15.3.3_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19.2.0_react@19.2.0__react@19.2.0\node_modules\next\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\next@15.3.3_@opentelemetry+api@1.9.0_@playwright+test@1.52.0_react-dom@19.2.0_react@19.2.0__react@19.2.0\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\next\dist\bin\next" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\next\dist\bin\next" %*
)
