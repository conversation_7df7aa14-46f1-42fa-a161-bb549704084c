#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\gel@2.0.2\node_modules\gel\dist\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\gel@2.0.2\node_modules\gel\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\gel@2.0.2\node_modules;H:\sub\vibed\supastarter-nextjs-main\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/gel@2.0.2/node_modules/gel/dist/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/gel@2.0.2/node_modules/gel/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/gel@2.0.2/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../node_modules/.pnpm/gel@2.0.2/node_modules/gel/dist/cli.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../node_modules/.pnpm/gel@2.0.2/node_modules/gel/dist/cli.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../node_modules/.pnpm/gel@2.0.2/node_modules/gel/dist/cli.mjs" $args
  } else {
    & "node$exe"  "$basedir/../../../../node_modules/.pnpm/gel@2.0.2/node_modules/gel/dist/cli.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
