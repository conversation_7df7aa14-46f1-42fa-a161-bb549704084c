#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/openai@5.1.1_zod@3.25.55/node_modules/openai/bin/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/openai@5.1.1_zod@3.25.55/node_modules/openai/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/openai@5.1.1_zod@3.25.55/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/openai@5.1.1_zod@3.25.55/node_modules/openai/bin/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/openai@5.1.1_zod@3.25.55/node_modules/openai/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/openai@5.1.1_zod@3.25.55/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../node_modules/.pnpm/openai@5.1.1_zod@3.25.55/node_modules/openai/bin/cli" "$@"
else
  exec node  "$basedir/../../../../node_modules/.pnpm/openai@5.1.1_zod@3.25.55/node_modules/openai/bin/cli" "$@"
fi
