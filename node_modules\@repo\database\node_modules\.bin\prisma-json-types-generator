#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/prisma-json-types-generator@3.4.2_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules/prisma-json-types-generator/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/prisma-json-types-generator@3.4.2_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/prisma-json-types-generator@3.4.2_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules/prisma-json-types-generator/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/prisma-json-types-generator@3.4.2_prisma@6.9.0_typescript@5.8.3__typescript@5.8.3/node_modules:/mnt/h/sub/vibed/supastarter-nextjs-main/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../prisma-json-types-generator/index.js" "$@"
else
  exec node  "$basedir/../prisma-json-types-generator/index.js" "$@"
fi
